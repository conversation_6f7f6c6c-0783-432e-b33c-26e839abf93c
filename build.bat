@echo off
echo Building Train Booking Application...

REM Create target directories
if not exist "target" mkdir target
if not exist "target\classes" mkdir target\classes
if not exist "target\tick" mkdir target\tick
if not exist "target\tick\WEB-INF" mkdir target\tick\WEB-INF
if not exist "target\tick\WEB-INF\classes" mkdir target\tick\WEB-INF\classes
if not exist "target\tick\WEB-INF\lib" mkdir target\tick\WEB-INF\lib

REM Copy web resources
xcopy /E /Y "src\main\webapp\*" "target\tick\"

REM Note: You'll need to compile Java files manually or use an IDE
echo.
echo Build structure created in target\tick
echo.
echo To complete the build:
echo 1. Compile Java files using your IDE (Eclipse)
echo 2. Copy compiled .class files to target\tick\WEB-INF\classes
echo 3. Copy required JAR files to target\tick\WEB-INF\lib
echo 4. Create WAR file from target\tick directory
echo.
pause
