package org.example.listener;

import org.example.util.DataInitializer;
import org.example.util.HibernateUtil;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

@WebListener
public class AppContextListener implements ServletContextListener {
    
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("Application starting up...");
        
        try {
            // Initialize Hibernate
            HibernateUtil.getSessionFactory();
            System.out.println("Hibernate initialized successfully");
            
            // Initialize sample data
            DataInitializer.initializeData();
            System.out.println("Sample data loaded successfully");
            
        } catch (Exception e) {
            System.err.println("Error during application startup: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("Application shutting down...");
        
        try {
            // Shutdown Hibernate
            HibernateUtil.shutdown();
            System.out.println("Hibernate shutdown successfully");
        } catch (Exception e) {
            System.err.println("Error during application shutdown: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
