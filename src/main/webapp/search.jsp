<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche de Trains - TrainBooking</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
        }
        .search-form {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: -30px;
            position: relative;
            z-index: 10;
        }
        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.jsp">
                <i class="fas fa-train"></i> TrainBooking
            </a>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container text-center">
            <h2><i class="fas fa-search"></i> Rechercher un train</h2>
            <p>Trouvez le train parfait pour votre voyage</p>
        </div>
    </section>

    <!-- Search Form -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="search-form">
                    <!-- Display messages -->
                    <c:if test="${not empty errorMessage}">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                        </div>
                    </c:if>
                    
                    <form action="search" method="post">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="departureCity" class="form-label">Ville de départ</label>
                                <select class="form-select" id="departureCity" name="departureCity" required>
                                    <option value="">Sélectionnez</option>
                                    <c:forEach var="city" items="${cities}">
                                        <option value="${city.name}">${city.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="arrivalCity" class="form-label">Ville d'arrivée</label>
                                <select class="form-select" id="arrivalCity" name="arrivalCity" required>
                                    <option value="">Sélectionnez</option>
                                    <c:forEach var="city" items="${cities}">
                                        <option value="${city.name}">${city.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="travelDate" class="form-label">Date de voyage</label>
                                <input type="date" class="form-control" id="travelDate" name="travelDate" 
                                       min="<%= java.time.LocalDate.now() %>" required>
                            </div>
                            
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-search w-100">
                                    <i class="fas fa-search"></i> Rechercher
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <h5><i class="fas fa-info-circle"></i> Comment rechercher ?</h5>
                    <ul class="mb-0">
                        <li>Sélectionnez votre ville de départ</li>
                        <li>Choisissez votre destination</li>
                        <li>Indiquez la date de votre voyage</li>
                        <li>Cliquez sur "Rechercher" pour voir les trains disponibles</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set minimum date to today
        document.getElementById('travelDate').min = new Date().toISOString().split('T')[0];
        
        // Prevent selecting same departure and arrival cities
        document.getElementById('departureCity').addEventListener('change', function() {
            const arrivalSelect = document.getElementById('arrivalCity');
            const selectedValue = this.value;
            
            Array.from(arrivalSelect.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });
        
        document.getElementById('arrivalCity').addEventListener('change', function() {
            const departureSelect = document.getElementById('departureCity');
            const selectedValue = this.value;
            
            Array.from(departureSelect.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });
    </script>
</body>
</html>
