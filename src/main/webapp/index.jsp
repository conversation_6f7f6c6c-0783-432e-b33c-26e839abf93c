<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Réservation de Billets de Train</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .search-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }
        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
        }
        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.jsp">
                <i class="fas fa-train"></i> TrainBooking
            </a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">
                <i class="fas fa-train"></i> Réservez vos billets de train
            </h1>
            <p class="lead">Trouvez et réservez facilement vos trajets en train</p>
        </div>
    </section>

    <!-- Search Form -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="search-card">
                    <h3 class="text-center mb-4">
                        <i class="fas fa-search"></i> Rechercher un train
                    </h3>
                    
                    <!-- Display error messages -->
                    <c:if test="${not empty errorMessage}">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                        </div>
                    </c:if>
                    
                    <form action="search" method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="departureCity" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Ville de départ
                                </label>
                                <select class="form-select" id="departureCity" name="departureCity" required>
                                    <option value="">Sélectionnez une ville</option>
                                    <option value="Paris">Paris</option>
                                    <option value="Lyon">Lyon</option>
                                    <option value="Marseille">Marseille</option>
                                    <option value="Toulouse">Toulouse</option>
                                    <option value="Nice">Nice</option>
                                    <option value="Bordeaux">Bordeaux</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="arrivalCity" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Ville d'arrivée
                                </label>
                                <select class="form-select" id="arrivalCity" name="arrivalCity" required>
                                    <option value="">Sélectionnez une ville</option>
                                    <option value="Paris">Paris</option>
                                    <option value="Lyon">Lyon</option>
                                    <option value="Marseille">Marseille</option>
                                    <option value="Toulouse">Toulouse</option>
                                    <option value="Nice">Nice</option>
                                    <option value="Bordeaux">Bordeaux</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="travelDate" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Date de voyage
                                </label>
                                <input type="date" class="form-control" id="travelDate" name="travelDate" 
                                       min="<%= java.time.LocalDate.now() %>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-search w-100">
                                    <i class="fas fa-search"></i> Rechercher
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <section class="py-5 mt-5 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                            <h5>Réservation rapide</h5>
                            <p class="text-muted">Réservez vos billets en quelques clics</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                            <h5>Paiement sécurisé</h5>
                            <p class="text-muted">Transactions 100% sécurisées</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                            <h5>Billets électroniques</h5>
                            <p class="text-muted">Recevez vos billets par email</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p>&copy; 2024 TrainBooking. Tous droits réservés.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set minimum date to today
        document.getElementById('travelDate').min = new Date().toISOString().split('T')[0];
        
        // Prevent selecting same departure and arrival cities
        document.getElementById('departureCity').addEventListener('change', function() {
            const arrivalSelect = document.getElementById('arrivalCity');
            const selectedValue = this.value;
            
            Array.from(arrivalSelect.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });
        
        document.getElementById('arrivalCity').addEventListener('change', function() {
            const departureSelect = document.getElementById('departureCity');
            const selectedValue = this.value;
            
            Array.from(departureSelect.options).forEach(option => {
                option.disabled = option.value === selectedValue && option.value !== '';
            });
        });
    </script>
</body>
</html>
