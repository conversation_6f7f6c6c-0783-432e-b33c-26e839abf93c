<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Train Ticket Booking System</display-name>

    <!-- Context Listener for initialization -->
    <listener>
        <listener-class>org.example.listener.AppContextListener</listener-class>
    </listener>

    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- Train Search Servlet -->
    <servlet>
        <servlet-name>TrainSearchServlet</servlet-name>
        <servlet-class>org.example.controller.TrainSearchServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>TrainSearchServlet</servlet-name>
        <url-pattern>/search</url-pattern>
    </servlet-mapping>

    <!-- Session timeout (30 minutes) -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

</web-app>
