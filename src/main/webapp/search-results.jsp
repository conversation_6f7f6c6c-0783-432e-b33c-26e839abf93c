<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résultats de Recherche - TrainBooking</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .results-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
        }
        .train-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        .train-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .price-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2em;
        }
        .time-info {
            font-size: 1.1em;
            font-weight: 600;
        }
        .duration-badge {
            background-color: #f8f9fa;
            color: #6c757d;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .seats-info {
            color: #28a745;
            font-weight: 500;
        }
        .search-summary {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: -30px;
            position: relative;
            z-index: 10;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.jsp">
                <i class="fas fa-train"></i> TrainBooking
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="search">
                    <i class="fas fa-search"></i> Nouvelle recherche
                </a>
            </div>
        </div>
    </nav>

    <!-- Results Header -->
    <section class="results-header">
        <div class="container text-center">
            <h2><i class="fas fa-list"></i> Résultats de recherche</h2>
            <p>Trains disponibles pour votre voyage</p>
        </div>
    </section>

    <!-- Search Summary -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="search-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-route"></i> 
                                ${searchCriteria.departureCity} → ${searchCriteria.arrivalCity}
                            </h5>
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar"></i> 
                                <fmt:formatDate value="${searchCriteria.travelDate}" pattern="dd/MM/yyyy"/>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-primary fs-6">
                                ${trains.size()} train(s) trouvé(s)
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                
                <!-- Display messages -->
                <c:if test="${not empty infoMessage}">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i> ${infoMessage}
                    </div>
                </c:if>
                
                <c:if test="${not empty errorMessage}">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                    </div>
                </c:if>

                <!-- Train Results -->
                <c:forEach var="train" items="${trains}">
                    <div class="card train-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <!-- Train Info -->
                                <div class="col-md-3">
                                    <h6 class="card-title mb-1">${train.trainName}</h6>
                                    <small class="text-muted">${train.trainNumber}</small>
                                </div>
                                
                                <!-- Time Info -->
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="text-center">
                                            <div class="time-info">${train.departureTime}</div>
                                            <small class="text-muted">${train.departureCity.name}</small>
                                        </div>
                                        <div class="text-center mx-3">
                                            <i class="fas fa-arrow-right text-primary"></i>
                                            <div class="duration-badge mt-1">${train.duration}</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="time-info">${train.arrivalTime}</div>
                                            <small class="text-muted">${train.arrivalCity.name}</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Availability -->
                                <div class="col-md-2 text-center">
                                    <div class="seats-info">
                                        <i class="fas fa-chair"></i> ${train.availableSeats} places
                                    </div>
                                    <small class="text-muted">disponibles</small>
                                </div>
                                
                                <!-- Price and Action -->
                                <div class="col-md-3 text-center">
                                    <div class="price-badge mb-2">
                                        <fmt:formatNumber value="${train.price}" type="currency" currencySymbol="€"/>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm" disabled>
                                        <i class="fas fa-ticket-alt"></i> Réserver
                                    </button>
                                    <br>
                                    <small class="text-muted">(Bientôt disponible)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:forEach>
                
                <!-- No results message -->
                <c:if test="${empty trains}">
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>Aucun train trouvé</h4>
                        <p class="text-muted">Essayez de modifier vos critères de recherche</p>
                        <a href="search" class="btn btn-primary">
                            <i class="fas fa-search"></i> Nouvelle recherche
                        </a>
                    </div>
                </c:if>
                
            </div>
        </div>
    </div>

    <!-- Back to search -->
    <div class="container mt-4 mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-10 text-center">
                <a href="search" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> Modifier la recherche
                </a>
                <a href="index.jsp" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-home"></i> Accueil
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
