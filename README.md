# Train Ticket Booking System

## Problème de connexion Tomcat

Si vous voyez une page de connexion, voici les solutions :

### Solution 1: Identifiants par défaut
Essayez ces combinaisons :
- Utilisateur: `admin`, Mot de passe: `admin`
- Utilisateur: `tomcat`, Mot de passe: `tomcat`
- Utilisateur: `admin`, Mot de passe: (vide)

### Solution 2: Configuration Tomcat
1. All<PERSON> dans le dossier d'installation de Tomcat
2. Ouvrez `conf/tomcat-users.xml`
3. Ajoutez ces lignes avant `</tomcat-users>` :

```xml
<role rolename="manager-gui"/>
<role rolename="admin-gui"/>
<user username="admin" password="admin" roles="manager-gui,admin-gui"/>
```

4. Redémarrez Tomcat

### Solution 3: Déploiement Eclipse
1. Dans Eclipse : Window → Show View → Servers
2. Clic droit → New → Server → Apache Tomcat
3. Clic droit sur le serveur → Add and Remove → Ajouter le projet "tick"
4. Démarrer le serveur
5. Accéder à : `http://localhost:8080/tick/`

### Solution 4: URL directe
Essayez d'accéder directement à :
- `http://localhost:8080/tick/`
- `http://localhost:8080/tick/index.jsp`
- `http://localhost:8080/tick/search`

### Structure du projet
```
src/
├── main/
│   ├── java/
│   │   └── org/example/
│   │       ├── controller/     # Servlets
│   │       ├── dao/           # Data Access Objects
│   │       ├── dto/           # Data Transfer Objects
│   │       ├── entity/        # JPA Entities
│   │       ├── listener/      # Context Listeners
│   │       ├── service/       # Business Logic
│   │       └── util/          # Utilities
│   ├── resources/
│   │   └── hibernate.cfg.xml  # Hibernate Configuration
│   └── webapp/
│       ├── WEB-INF/
│       │   └── web.xml        # Web Configuration
│       ├── index.jsp          # Page d'accueil
│       ├── search.jsp         # Page de recherche
│       └── search-results.jsp # Résultats
```

### Fonctionnalités implémentées
- ✅ Recherche de trains sans authentification
- ✅ Recherche par ville de départ, destination et date
- ✅ Affichage des détails : horaires, durée, prix, places disponibles
- ✅ Interface responsive en français
- ✅ Base de données H2 avec données d'exemple
- ✅ Architecture MVC avec DAO pattern

### Technologies utilisées
- Java EE (Servlets, JSP, JSTL, EL)
- Hibernate ORM
- H2 Database
- Bootstrap 5
- Font Awesome
